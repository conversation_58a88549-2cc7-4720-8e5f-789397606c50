import express from 'express';
import fetch from 'node-fetch';
import { execa } from 'execa';
import fs from 'node:fs';
import fsp from 'node:fs/promises';
import path from 'node:path';
import os from 'node:os';
import { fileURLToPath } from 'node:url';
import { v4 as uuidv4 } from 'uuid';
import tmp from 'tmp';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { renderOverlayFrames, getRegisteredFonts, getCuratedFonts, resolveCuratedFont, fontManager } from './renderer.js';

const app = express();
app.use(express.json({ limit: '5mb' }));

// Simple bearer auth
function requireAuth(req, res, next) {
  const token = (req.headers['authorization'] || '').replace(/^Bearer\s+/i, '').trim();
  if (!token || token !== process.env.EXPORT_SERVICE_SECRET) {
    return res.status(401).json({ error: 'unauthorized' });
  }
  next();
}

// In-memory job store (replace with Redis/DB for durability)
const jobs = new Map();

// R2 S3 client
const s3 = new S3Client({
  region: 'auto',
  endpoint: `https://${process.env.R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID,
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY
  }
});

// Detect NVENC availability once at startup
let nvencAvailable = false;
(async () => {
  try {
    const { stdout } = await execa('ffmpeg', ['-hide_banner', '-encoders']);
    nvencAvailable = /h264_nvenc/.test(stdout || '');
    console.log('NVENC available:', nvencAvailable);
  } catch (e) {
    console.warn('Failed to detect NVENC:', e.message);
  }
})();

// Font validation is now handled by curated font system

async function downloadToTemp(url) {
  const r = await fetch(url);
  if (!r.ok) throw new Error(`download_failed ${r.status}`);
  const tmpFile = tmp.fileSync({ postfix: path.extname(new URL(url).pathname) || '.mp4' });
  const ws = fs.createWriteStream(tmpFile.name);
  await new Promise((res, rej) => {
    r.body.pipe(ws);
    r.body.on('error', rej);
    ws.on('finish', res);
    ws.on('error', rej);
  });
  return tmpFile.name;
}

async function ffprobeJson(file) {
  const { stdout } = await execa('/usr/local/bin/ffprobe', ['-v', 'error', '-print_format', 'json', '-show_format', '-show_streams', file]);
  return JSON.parse(stdout);
}

async function uploadToR2(localPath, key, contentType) {
  await s3.send(new PutObjectCommand({
    Bucket: process.env.R2_BUCKET,
    Key: key,
    Body: fs.createReadStream(localPath),
    ContentType: contentType
  }));
  return key;
}

// Health check endpoint
app.get('/health', (req, res) => {
  try {
    const fontInfo = getRegisteredFonts();
    res.json({ 
      status: 'healthy',
      timestamp: new Date().toISOString(),
      fonts: {
        total: fontInfo.totalFonts,
        registered: fontInfo.registeredFonts,
        families: fontInfo.families.length
      },
      nvenc: nvencAvailable
    });
  } catch (error) {
    res.status(500).json({ 
      status: 'unhealthy', 
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// API endpoint to get curated fonts list
app.get('/fonts', (req, res) => {
  try {
    const curatedFonts = getCuratedFonts();
    res.json({ fonts: curatedFonts });
  } catch (error) {
    console.error('[API] Get curated fonts failed:', error);
    res.status(500).json({ error: 'Failed to get fonts', details: error.message });
  }
});

// Debug endpoint to check registered fonts
app.get('/fonts/debug', requireAuth, (req, res) => {
  try {
    const debugInfo = getRegisteredFonts();
    res.json(debugInfo);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/jobs', requireAuth, async (req, res) => {
  try {
    const id = uuidv4();
    const payload = req.body || {};
    jobs.set(id, { id, status: 'queued', payload });

    // Fire-and-forget worker
    processJob(id).catch(err => {
      console.error('job failed', id, err);
      const j = jobs.get(id);
      if (j) { 
        j.status = 'error'; 
        j.error = String(err?.message || err);
        j.errorDetails = err.stack || '';
        jobs.set(id, j); 
      }
    });
    res.json({ jobId: id });
  } catch (e) {
    console.error(e);
    res.status(500).json({ error: 'create_failed' });
  }
});

app.get('/jobs/:id', requireAuth, (req, res) => {
  const j = jobs.get(req.params.id);
  if (!j) return res.status(404).json({ error: 'not_found' });
  res.json({ 
    status: j.status, 
    result: j.result, 
    error: j.error,
    fontInfo: j.fontInfo // Include font processing info
  });
});

async function processJob(id) {
  const j = jobs.get(id);
  if (!j) return;
  j.status = 'processing'; 
  jobs.set(id, j);

  const { source, segments, captionStyle, videoFX, watermark, output, userId } = j.payload;
  const signedURL = source?.signedURL;
  if (!signedURL) throw new Error('no_source');

  // Font handling is now done via curated fonts - no custom uploads

  // Ensure fonts are initialized before processing
  console.log('[Job] Ensuring fonts are initialized...');
  await fontManager.initialize();

  // Validate that the requested font ID is available
  if (captionStyle?.fontId) {
    try {
      const testFont = resolveCuratedFont(captionStyle.fontId);
      console.log(`[Job] Font validation passed: ${captionStyle.fontId} -> ${testFont.family} ${testFont.weight}`);

      // Update job with resolved font info
      j.fontInfo = {
        requested: {
          fontId: captionStyle.fontId
        },
        resolved: {
          family: testFont.family,
          weight: testFont.weight,
          style: testFont.style
        },
        success: true
      };
      jobs.set(id, j);

    } catch (error) {
      throw new Error(`Font validation failed: ${error.message}`);
    }
  }

  // 1) Download source video
  console.log('[Job] Downloading source video...');
  const inputPath = await downloadToTemp(signedURL);

  // 2) Probe video metadata
  console.log('[Job] Probing video metadata...');
  const meta = await ffprobeJson(inputPath);
  const vStream = (meta.streams || []).find(s => s.codec_type === 'video');
  const srcW = Number(vStream?.width || 1280);
  const srcH = Number(vStream?.height || 720);
  const fps = (() => {
    const r = (vStream?.r_frame_rate || '30/1').split('/');
    const f = Number(r[0]) / Number(r[1] || 1);
    return Math.max(15, Math.min(60, Math.round(f || 30)));
  })();
  const inputDuration = Number(meta.format?.duration || 0) || 0;
  const rotationDeg = getRotationDeg(vStream);
  const swap = Math.abs(rotationDeg) % 180 === 90;
  const width = swap ? srcH : srcW;
  const height = swap ? srcW : srcH;

  console.log(`[Job] Video: ${width}x${height} @ ${fps}fps, duration: ${inputDuration}s`);

  // 3) Prepare working directory and render overlay frames
  console.log('[Job] Rendering overlay frames...');
  const tmpDir = await fsp.mkdtemp(path.join(os.tmpdir(), 'cap-'));
  const framesDir = path.join(tmpDir, 'overlay_frames');
  await fsp.mkdir(framesDir, { recursive: true });
  
  try {
    await renderOverlayFrames({
      framesDir,
      width, height, fps,
      segments: Array.isArray(segments) ? segments : [],
      style: captionStyle || {},
      watermark: !!watermark,
      videoDuration: inputDuration
    });
  } catch (error) {
    throw new Error(`Frame rendering failed: ${error.message}`);
  }

  // 4) Build FFmpeg filters for video effects
  console.log('[Job] Preparing video effects...');
  const fx = videoFX || {};
  const brightnessAdj = (() => {
    const b = Number(fx.brightness ?? 1);
    const adj = b - 1;
    return Math.max(-0.5, Math.min(0.5, isFinite(adj) ? adj : 0));
  })();
  const eq = `eq=saturation=${Number(fx.saturate||1)}:contrast=${Number(fx.contrast||1)}:brightness=${brightnessAdj}`;
  const hueDeg = Number(fx.hue || 0);
  const hue = (hueDeg !== 0) ? `hue=H=${(hueDeg * Math.PI / 180).toFixed(6)}` : null;
  const sepia = Number(fx.sepia||0) > 0 ? 'colorchannelmixer=.393:.769:.189:0:.349:.686:.168:0:.272:.534:.131' : null;
  const gray = Number(fx.grayscale||0) > 0 ? 'format=gray' : null;
  const invert = Number(fx.invert||0) > 0 ? 'lutrgb=r=negval:g=negval:b=negval' : null;
  const blur = Number(fx.blur||0) > 0 ? `gblur=sigma=${Math.max(0.1, Number(fx.blur))}` : null;
  const baseFilters = [eq, hue, sepia, gray, invert, blur].filter(Boolean).join(',');

  // 5) Encode final video
  console.log('[Job] Encoding final video...');
  const outPath = path.join(tmpDir, 'out.mp4');
  const crf = String(output?.crf ?? 20);
  const preset = String(output?.preset ?? 'veryfast');
  const srcVideoBitrate = Number(vStream?.bit_rate || 0);
  const targetBitrate = srcVideoBitrate && isFinite(srcVideoBitrate) && srcVideoBitrate > 0 ? Math.round(srcVideoBitrate) : 0;
  
  // Overlay PNG sequence with alpha
  const fullVf = `[0:v]${baseFilters}[vf];[1:v]format=rgba,setsar=1[ov];[vf][ov]overlay=shortest=1:format=auto[vout]`;
  const useNvenc = nvencAvailable && !process.env.FORCE_CPU;
  
  const ffArgs = [
    '-nostdin', '-y', '-i', inputPath,
    '-framerate', String(fps), '-thread_queue_size', '512', '-i', path.join(framesDir, '%06d.png'),
    '-filter_complex', fullVf,
    '-map', '[vout]', '-map', '0:a:0?',
    '-c:v', useNvenc ? 'h264_nvenc' : 'libx264',
    ...(useNvenc ? ['-preset', 'p5', '-rc', 'vbr'] : ['-preset', preset]),
    '-r', String(fps), '-g', String(Math.max(1, fps*2)), '-pix_fmt', 'yuv420p',
    ...(useNvenc ? [] : ['-threads', '0']),
    '-c:a', 'aac', '-b:a', String(output?.audioBitrate || 160000), '-movflags', '+faststart',
    outPath
  ];
  
  if (targetBitrate > 0) {
    const insertAt = ffArgs.indexOf('-r');
    ffArgs.splice(insertAt, 0, ...(useNvenc ? ['-cq', '23'] : []), '-b:v', String(targetBitrate), '-maxrate', String(Math.round(targetBitrate*1.5)), '-bufsize', String(Math.round(targetBitrate*3)));
  } else {
    const insertAt = ffArgs.indexOf('-r');
    ffArgs.splice(insertAt, 0, ...(useNvenc ? ['-cq', crf] : ['-crf', crf]));
  }
  
  try {
    await execa('ffmpeg', ffArgs);
  } catch (e) {
    // If NVENC failed, retry on CPU
    if (useNvenc) {
      console.warn('[Job] NVENC failed, retrying with CPU encoding...');
      const cpuArgs = [
        '-nostdin', '-y', '-i', inputPath,
        '-framerate', String(fps), '-thread_queue_size', '512', '-i', path.join(framesDir, '%06d.png'),
        '-filter_complex', fullVf,
        '-map', '[vout]', '-map', '0:a:0?',
        '-c:v', 'libx264', '-preset', preset, '-r', String(fps), '-g', String(Math.max(1, fps*2)), '-pix_fmt', 'yuv420p', '-threads', '0',
        '-c:a', 'aac', '-b:a', String(output?.audioBitrate || 160000), '-movflags', '+faststart',
        outPath
      ];
      if (targetBitrate > 0) {
        const ins = cpuArgs.indexOf('-r');
        cpuArgs.splice(ins, 0, '-b:v', String(targetBitrate), '-maxrate', String(Math.round(targetBitrate*1.5)), '-bufsize', String(Math.round(targetBitrate*3)));
      } else {
        const ins = cpuArgs.indexOf('-r');
        cpuArgs.splice(ins, 0, '-crf', crf);
      }
      await execa('ffmpeg', cpuArgs);
    } else {
      throw e;
    }
  }

  // 6) Upload to R2
  console.log('[Job] Uploading to R2...');
  const outKey = `exports/${(j.payload.userId||'user')}/${id}.mp4`;
  await uploadToR2(outPath, outKey, 'video/mp4');

  j.status = 'completed';
  j.result = { r2Key: outKey };
  jobs.set(id, j);

  console.log(`[Job] Completed successfully: ${id}`);

  // Cleanup
  try { 
    await fsp.rm(tmpDir, { recursive: true, force: true }); 
  } catch (e) {
    console.warn('[Job] Cleanup failed:', e.message);
  }
}

const port = process.env.PORT || 8080;
app.listen(port, () => console.log(`Exporter listening on ${port}`));

// Helper function for video rotation metadata
function getRotationDeg(vStream){
  try {
    const side = vStream?.side_data_list || vStream?.side_data || [];
    for (const s of side) {
      if (!s) continue;
      if (typeof s.rotation === 'number') return Number(s.rotation);
      if (typeof s.displaymatrix === 'string' && /rotation of ([\-\d.]+)/i.test(s.displaymatrix)) {
        const m = s.displaymatrix.match(/rotation of ([\-\d.]+)/i);
        return m ? Number(m[1]) : 0;
      }
    }
  } catch {}
  return 0;
}