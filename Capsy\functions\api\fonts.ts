// API endpoint to get available curated fonts
export async function onRequestGet(ctx: any) {
  // Return the exact same curated fonts list as defined in the exporter
  // This ensures consistency and avoids network issues
  const curatedFonts = [
    {
      id: 'inter',
      name: 'Inter',
      description: 'Modern, clean sans-serif (default)',
      category: 'sans-serif'
    },
    {
      id: 'anton',
      name: '<PERSON>',
      description: 'Bold, condensed display font',
      category: 'display'
    },
    {
      id: 'archivo-black',
      name: 'Archivo Black',
      description: 'Heavy, impactful sans-serif',
      category: 'display'
    },
    {
      id: 'barlow',
      name: 'Barlow ExtraBold',
      description: 'Strong, geometric sans-serif',
      category: 'sans-serif'
    },
    {
      id: 'bebas-neue',
      name: '<PERSON><PERSON>',
      description: 'Tall, condensed display font',
      category: 'display'
    },
    {
      id: 'lexend',
      name: 'Lexend ExtraBold',
      description: 'Readable, optimized for comprehension',
      category: 'sans-serif'
    },
    {
      id: 'montserrat',
      name: '<PERSON><PERSON><PERSON> ExtraBold',
      description: 'Popular, versatile sans-serif',
      category: 'sans-serif'
    },
    {
      id: 'os<PERSON>',
      name: '<PERSON>',
      description: 'Condensed, strong sans-serif',
      category: 'sans-serif'
    },
    {
      id: 'outfit',
      name: 'Outfit ExtraBold',
      description: 'Modern, rounded sans-serif',
      category: 'sans-serif'
    },
    {
      id: 'poppins',
      name: 'Poppins ExtraBold',
      description: 'Friendly, geometric sans-serif',
      category: 'sans-serif'
    }
  ];

  return new Response(JSON.stringify({ fonts: curatedFonts }), {
    headers: {
      "content-type": "application/json",
      "cache-control": "public, max-age=3600" // Cache for 1 hour
    }
  });
}
