{"name": "capsy-exporter", "private": true, "type": "module", "version": "0.2.0", "description": "Improved video caption exporter with robust font handling", "scripts": {"start": "node server_improved.js", "start:original": "node server.js", "dev": "NODE_ENV=development node server.js", "test:fonts": "node -e \"import('./renderer.js').then(r => console.log(JSON.stringify(r.getRegisteredFonts(), null, 2)))\""}, "dependencies": {"@aws-sdk/client-s3": "^3.637.0", "express": "^4.19.2", "execa": "^8.0.1", "node-fetch": "^3.3.2", "p-queue": "^7.4.1", "tmp": "^0.2.3", "uuid": "^9.0.1", "canvas": "^2.11.2", "opentype.js": "^1.3.4"}, "engines": {"node": ">=18.0.0"}, "keywords": ["video", "captions", "fonts", "ffmpeg", "canvas"]}