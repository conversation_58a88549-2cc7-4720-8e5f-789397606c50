# Stage 1: Build FFmpeg with NVENC support
FROM ubuntu:22.04 AS ffmpeg_build
ENV DEBIAN_FRONTEND=noninteractive

RUN apt-get update && apt-get install -y --no-install-recommends \
  build-essential pkg-config git curl ca-certificates \
  yasm nasm cmake python3 \
  libass-dev libfreetype6-dev libfribidi-dev libharfbuzz-dev \
  libfontconfig1-dev && rm -rf /var/lib/apt/lists/*

RUN git clone https://github.com/FFmpeg/nv-codec-headers.git /tmp/nv && \
    cd /tmp/nv && git checkout n12.1.14.0 && make && make install

ARG FFMPEG_VERSION=7.0.2
RUN curl -fsSL https://ffmpeg.org/releases/ffmpeg-${FFMPEG_VERSION}.tar.bz2 -o /tmp/ffmpeg.tar.bz2 && \
    mkdir -p /tmp/ffmpeg && tar -xjf /tmp/ffmpeg.tar.bz2 -C /tmp/ffmpeg --strip-components=1 && \
    cd /tmp/ffmpeg && \
    ./configure --prefix=/opt/ffmpeg \
      --disable-debug --disable-doc --disable-ffplay \
      --enable-gpl --enable-version3 \
      --enable-libass --enable-libfreetype --enable-libfribidi --enable-libharfbuzz --enable-fontconfig \
      --enable-nonfree --enable-nvenc --enable-pthreads && \
    make -j$(nproc ) && make install

# Stage 2: Setup application runtime
FROM ubuntu:22.04
ENV DEBIAN_FRONTEND=noninteractive

# Install Node.js 20, ffmpeg runtime dependencies, node-canvas dependencies, and essential fonts
RUN apt-get update && apt-get install -y --no-install-recommends \
  curl ca-certificates gnupg \
  fontconfig fonts-dejavu-core fonts-dejavu-extra \
  libass9 libfreetype6 libfribidi0 libharfbuzz0b libfontconfig1 \
  libcairo2 libpango-1.0-0 libpangoft2-1.0-0 libpangocairo-1.0-0 \
  libjpeg-turbo8 libgif7 \
  libxcb1 libxcb-shm0 libxcb-shape0 libxcb-xfixes0 libx11-6 libxext6 libdrm2 \
  libsdl2-2.0-0 libasound2 \
  build-essential \
  && \
  curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
  apt-get install -y --no-install-recommends nodejs && \
  rm -rf /var/lib/apt/lists/*

# Copy Inter font (modern, high-quality default )
RUN set -eux; \
    apt-get update && apt-get install -y --no-install-recommends wget unzip && \
    wget -q https://github.com/rsms/inter/releases/download/v4.1/Inter-4.1.zip -O /tmp/inter.zip && \
    mkdir -p /usr/share/fonts/truetype/inter && \
    unzip -q /tmp/inter.zip -d /usr/share/fonts/truetype/inter && \
    rm -rf /tmp/inter.zip && \
    apt-get purge -y unzip wget && apt-get autoremove -y && rm -rf /var/lib/apt/lists/*

# Copy your repo fonts into system fonts (Dockerfile is in exporter/)
# This copies everything under exporter/fonts/** into /usr/share/fonts/truetype/custom/
COPY fonts/ /usr/share/fonts/truetype/custom/
RUN fc-cache -f -v

# Install FFmpeg from builder
COPY --from=ffmpeg_build /opt/ffmpeg/ /usr/local/
ENV PATH="/usr/local/bin:${PATH}"
ENV LD_LIBRARY_PATH="/usr/local/lib:${LD_LIBRARY_PATH}"

# App
WORKDIR /app
COPY package*.json ./
RUN npm install --omit=dev
COPY . .

ENV NODE_ENV=production
ENV PORT=8080
EXPOSE 8080
ENTRYPOINT ["node", "server.js"]

