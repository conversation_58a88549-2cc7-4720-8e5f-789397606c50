// API endpoint to get available curated fonts
export async function onRequestGet(ctx: any) {
  const { env } = ctx;
  
  // Get exporter service URL
  const exporterURL = env.EXPORTER_URL;
  if (!exporterURL) {
    return new Response(JSON.stringify({ error: "exporter_not_configured" }), { 
      status: 500,
      headers: { "content-type": "application/json" }
    });
  }

  try {
    // Fetch fonts from exporter service
    const response = await fetch(`${exporterURL.replace(/\/$/, '')}/fonts`, {
      method: 'GET',
      headers: {
        'content-type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Exporter service error: ${response.status}`);
    }

    const data = await response.json();
    
    return new Response(JSON.stringify(data), {
      headers: { 
        "content-type": "application/json",
        "cache-control": "public, max-age=3600" // Cache for 1 hour
      }
    });

  } catch (error) {
    console.error('Failed to fetch fonts from exporter:', error);
    
    // Return fallback font list
    const fallbackFonts = [
      {
        id: 'inter',
        name: 'Inter',
        description: 'Modern, clean sans-serif (default)',
        category: 'sans-serif'
      }
    ];
    
    return new Response(JSON.stringify({ fonts: fallbackFonts }), {
      headers: { "content-type": "application/json" }
    });
  }
}
