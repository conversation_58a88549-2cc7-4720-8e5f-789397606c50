import fs from 'node:fs';
import fsp from 'node:fs/promises';
import path from 'node:path';
import { createCanvas, registerFont } from 'canvas';
import opentype from 'opentype.js';

// Font registry with detailed metadata
const FONT_REGISTRY = new Map(); // key: "family:weight:style", value: { path, family, weight, style, registered }

// Font validation and metadata extraction
class FontManager {
  constructor() {
    this.initialized = false;
  }

  /**
   * Extract font metadata directly from font file using opentype.js
   */
  async extractFontMetadata(fontPath) {
    let font = null;
    try {
      const buffer = await fsp.readFile(fontPath);
      font = opentype.parse(buffer.buffer);
      
      // Ensure font object is valid and has expected tables
      if (!font || !font.tables || !font.tables.name) {
        throw new Error('Invalid font structure or missing name table');
      }

      // Extract family name (prefer English names)
      const familyName = this.getLocalizedName(font, 'fontFamily') || 
                        this.getLocalizedName(font, 'preferredFamily') ||
                        path.basename(fontPath, path.extname(fontPath));
      
      // Extract subfamily (style) name
      const subfamily = this.getLocalizedName(font, 'fontSubfamily') || 
                       this.getLocalizedName(font, 'preferredSubfamily') ||
                       'Regular';
      
      // Determine weight from OS/2 table or subfamily name
      const weight = this.extractWeight(font, subfamily);
      
      // Determine style (italic/oblique)
      const style = this.extractStyle(font, subfamily);
      
      return {
        family: familyName.trim(),
        weight: String(weight),
        style: style,
        subfamily: subfamily,
        path: fontPath,
        valid: true
      };
    } catch (error) {
      console.warn(`[FontManager] Failed to parse font ${fontPath}:`, error.message);
      return {
        family: path.basename(fontPath, path.extname(fontPath)), // Fallback to filename
        weight: '400',
        style: 'normal',
        subfamily: 'Regular',
        path: fontPath,
        valid: false,
        error: error.message
      };
    }
  }

  /**
   * Get localized name from font name table, preferring English
   */
  getLocalizedName(font, nameType) {
    const nameTable = font.tables.name;
    // Defensive check: ensure nameTable exists and has a 'get' method
    if (!nameTable || typeof nameTable.get !== 'function') {
      console.warn(`[FontManager] Name table or its 'get' method is missing for font: ${font.names?.fontFamily?.en || 'Unknown'}`);
      return null;
    }

    const nameIds = {
      'fontFamily': 1,
      'fontSubfamily': 2,
      'preferredFamily': 16,
      'preferredSubfamily': 17
    };

    const nameId = nameIds[nameType];
    if (!nameId) return null;

    // Look for English name first
    let name = nameTable.get(nameId, 0x0409); // English US
    if (!name) {
      name = nameTable.get(nameId, 0x0000); // Any language
    }
    if (!name && nameTable.names) {
      // Fallback: find any name with the right nameID
      const nameRecord = nameTable.names.find(n => n.nameID === nameId);
      name = nameRecord?.text;
    }

    return name;
  }

  /**
   * Extract font weight from OS/2 table or subfamily name
   */
  extractWeight(font, subfamily) {
    // Try OS/2 table first
    if (font.tables.os2 && font.tables.os2.usWeightClass) {
      return font.tables.os2.usWeightClass;
    }

    // Fallback to subfamily name parsing
    const sub = subfamily.toLowerCase();
    if (/\b(thin|hairline)\b/.test(sub)) return 100;
    if (/\b(extra[-\s]?light|ultra[-\s]?light)\b/.test(sub)) return 200;
    if (/\blight\b/.test(sub)) return 300;
    if (/\b(normal|regular|book|roman)\b/.test(sub)) return 400;
    if (/\bmedium\b/.test(sub)) return 500;
    if (/\b(semi[-\s]?bold|demi[-\s]?bold)\b/.test(sub)) return 600;
    if (/\bbold\b/.test(sub)) return 700;
    if (/\b(extra[-\s]?bold|ultra[-\s]?bold)\b/.test(sub)) return 800;
    if (/\b(black|heavy)\b/.test(sub)) return 900;
    
    return 400; // Default to normal
  }

  /**
   * Extract font style from head table or subfamily name
   */
  extractStyle(font, subfamily) {
    // Check head table macStyle
    if (font.tables.head && font.tables.head.macStyle) {
      const macStyle = font.tables.head.macStyle;
      if (macStyle & 0x02) return 'italic'; // Italic bit
    }

    // Check OS/2 table selection
    if (font.tables.os2 && font.tables.os2.fsSelection) {
      const fsSelection = font.tables.os2.fsSelection;
      if (fsSelection & 0x01) return 'italic'; // Italic bit
      if (fsSelection & 0x200) return 'oblique'; // Oblique bit
    }

    // Fallback to subfamily name
    const sub = subfamily.toLowerCase();
    if (/\b(italic|oblique)\b/.test(sub)) {
      return /\boblique\b/.test(sub) ? 'oblique' : 'italic';
    }

    return 'normal';
  }

  /**
   * Register a font with node-canvas and add to registry
   */
  async registerFont(fontPath, metadata = null) {
    try {
      // Extract metadata if not provided
      if (!metadata) {
        metadata = await this.extractFontMetadata(fontPath);
      }

      if (!metadata.valid) {
        throw new Error(`Invalid font file: ${metadata.error || 'Unknown error'}`);
      }

      // Create registry key
      const key = `${metadata.family}:${metadata.weight}:${metadata.style}`;
      
      // Check if already registered
      if (FONT_REGISTRY.has(key)) {
        const existing = FONT_REGISTRY.get(key);
        if (existing.registered) {
          console.log(`[FontManager] Font already registered: ${key}`);
          return existing;
        }
      }

      // Register with node-canvas
      registerFont(fontPath, {
        family: metadata.family,
        weight: metadata.weight,
        style: metadata.style
      });

      // Add to registry
      const fontInfo = {
        ...metadata,
        registered: true,
        registeredAt: new Date().toISOString()
      };
      
      FONT_REGISTRY.set(key, fontInfo);
      
      console.log(`[FontManager] Successfully registered font: ${key} from ${fontPath}`);
      return fontInfo;

    } catch (error) {
      console.error(`[FontManager] Failed to register font ${fontPath}:`, error.message);
      throw error;
    }
  }

  /**
   * Register fonts from a directory
   */
  async registerFontsFromDirectory(dirPath, recursive = true) {
    const results = { success: [], failed: [] };
    
    try {
      if (!fs.existsSync(dirPath)) {
        console.warn(`[FontManager] Directory does not exist: ${dirPath}`);
        return results;
      }

      const entries = await fsp.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory() && recursive) {
          const subResults = await this.registerFontsFromDirectory(fullPath, recursive);
          results.success.push(...subResults.success);
          results.failed.push(...subResults.failed);
        } else if (entry.isFile() && /\.(ttf|otf)$/i.test(entry.name)) {
          try {
            const fontInfo = await this.registerFont(fullPath);
            results.success.push(fontInfo);
          } catch (error) {
            results.failed.push({ path: fullPath, error: error.message });
          }
        }
      }
    } catch (error) {
      console.error(`[FontManager] Error scanning directory ${dirPath}:`, error.message);
    }

    return results;
  }

  /**
   * Initialize font manager with system and bundled fonts
   */
  async initialize() {
    if (this.initialized) return;

    console.log('[FontManager] Initializing font system...');
    
    const fontDirs = [
      '/usr/share/fonts/truetype/custom',
      '/usr/share/fonts/truetype/dejavu',
      '/usr/share/fonts/truetype/inter',
      path.join(process.cwd(), 'fonts'),
      path.join(process.cwd(), 'exporter', 'fonts')
    ];

    let totalSuccess = 0;
    let totalFailed = 0;

    for (const dir of fontDirs) {
      const results = await this.registerFontsFromDirectory(dir);
      totalSuccess += results.success.length;
      totalFailed += results.failed.length;
      
      if (results.success.length > 0) {
        console.log(`[FontManager] Registered ${results.success.length} fonts from ${dir}`);
      }
      if (results.failed.length > 0) {
        console.warn(`[FontManager] Failed to register ${results.failed.length} fonts from ${dir}`);
      }
    }

    console.log(`[FontManager] Initialization complete. Registered ${totalSuccess} fonts, ${totalFailed} failed.`);

    // Debug: List all registered fonts
    const registeredFonts = Array.from(FONT_REGISTRY.values())
      .filter(f => f.registered)
      .map(f => `${f.family}:${f.weight}:${f.style}`)
      .sort();
    console.log(`[FontManager] Available fonts: ${registeredFonts.join(', ')}`);

    this.initialized = true;
  }

  /**
   * Find the best matching font for a given family and weight
   */
  findBestMatch(requestedFamily, requestedWeight = 400, requestedStyle = 'normal') {
    const normalizedFamily = requestedFamily.trim();
    const targetWeight = Number(requestedWeight) || 400;

    // First, try exact family match
    const exactMatches = Array.from(FONT_REGISTRY.values())
      .filter(font => font.family === normalizedFamily && font.registered);

    if (exactMatches.length > 0) {
      return this.selectBestWeightMatch(exactMatches, targetWeight, requestedStyle);
    }

    // Try case-insensitive family match
    const caseInsensitiveMatches = Array.from(FONT_REGISTRY.values())
      .filter(font => font.family.toLowerCase() === normalizedFamily.toLowerCase() && font.registered);

    if (caseInsensitiveMatches.length > 0) {
      return this.selectBestWeightMatch(caseInsensitiveMatches, targetWeight, requestedStyle);
    }

    // Try partial matching - check if the requested family is contained in any registered font family
    // This handles cases where frontend sends "Anton" but font is registered as "Anton-Regular"
    const partialMatches = Array.from(FONT_REGISTRY.values())
      .filter(font => {
        if (!font.registered) return false;
        const fontFamily = font.family.toLowerCase();
        const requestedLower = normalizedFamily.toLowerCase();

        // Check if requested family is a substring of the font family
        // or if font family starts with the requested family
        return fontFamily.includes(requestedLower) ||
               fontFamily.startsWith(requestedLower) ||
               requestedLower.includes(fontFamily);
      });

    if (partialMatches.length > 0) {
      console.log(`[FontManager] Using partial match for '${normalizedFamily}': found ${partialMatches.length} candidates`);
      return this.selectBestWeightMatch(partialMatches, targetWeight, requestedStyle);
    }

    // No match found
    return null;
  }

  /**
   * Select the best weight and style match from a list of fonts
   */
  selectBestWeightMatch(fonts, targetWeight, targetStyle) {
    // First, filter by style if possible
    let candidates = fonts.filter(font => font.style === targetStyle);
    if (candidates.length === 0) {
      candidates = fonts; // Fallback to any style
    }

    // Find closest weight
    let bestFont = candidates[0];
    let bestDiff = Math.abs(Number(bestFont.weight) - targetWeight);

    for (const font of candidates) {
      const diff = Math.abs(Number(font.weight) - targetWeight);
      if (diff < bestDiff) {
        bestFont = font;
        bestDiff = diff;
      }
    }

    return bestFont;
  }

  /**
   * Resolve font stack to actual registered font
   */
  resolveFontStack(fontStack, weight = 400, style = 'normal') {
    const families = fontStack.split(',').map(f => f.trim().replace(/^["']|["']$/g, ''));

    console.log(`[FontManager] Resolving font stack: ${fontStack} (weight: ${weight}, style: ${style})`);
    console.log(`[FontManager] Parsed families: ${families.join(', ')}`);

    for (const family of families) {
      // Map generic families
      const mappedFamily = this.mapGenericFamily(family);
      console.log(`[FontManager] Trying family: '${family}' -> '${mappedFamily}'`);

      const match = this.findBestMatch(mappedFamily, weight, style);

      if (match) {
        console.log(`[FontManager] Found match: ${match.family} ${match.weight} ${match.style}`);
        return match;
      } else {
        console.log(`[FontManager] No match found for: '${mappedFamily}'`);
      }
    }

    // Ultimate fallback - any available font
    const anyFont = Array.from(FONT_REGISTRY.values()).find(font => font.registered);
    if (anyFont) {
      console.warn(`[FontManager] Using fallback font: ${anyFont.family}-${anyFont.weight}:${anyFont.style}`);
      return anyFont;
    }

    throw new Error('No fonts available in registry');
  }

  /**
   * Map generic CSS font families to actual fonts
   */
  mapGenericFamily(family) {
    const generic = family.toLowerCase();
    
    const mappings = {
      'serif': 'DejaVu Serif',
      'sans-serif': 'DejaVu Sans',
      'monospace': 'DejaVu Sans Mono',
      'system-ui': 'DejaVu Sans',
      '-apple-system': 'DejaVu Sans',
      'segoe ui': 'DejaVu Sans',
      'helvetica': 'DejaVu Sans',
      'arial': 'DejaVu Sans',
      'georgia': 'DejaVu Serif',
      'times': 'DejaVu Serif',
      'courier': 'DejaVu Sans Mono'
    };

    return mappings[generic] || family;
  }

  /**
   * Get debug information about registered fonts
   */
  getDebugInfo() {
    const fonts = Array.from(FONT_REGISTRY.values());
    const families = [...new Set(fonts.map(f => f.family))];
    
    return {
      totalFonts: fonts.length,
      registeredFonts: fonts.filter(f => f.registered).length,
      families: families.sort(),
      fonts: fonts.map(f => ({
        family: f.family,
        weight: f.weight,
        style: f.style,
        registered: f.registered,
        path: f.path
      }))
    };
  }
}

// Curated font definitions - these are the ONLY fonts we support
const CURATED_FONTS = [
  {
    id: 'inter',
    name: 'Inter',
    family: 'Inter',
    weight: '800',
    style: 'normal',
    description: 'Modern, clean sans-serif (default)',
    category: 'sans-serif'
  },
  {
    id: 'anton',
    name: 'Anton',
    family: 'Anton-Regular',
    weight: '400',
    style: 'normal',
    description: 'Bold, condensed display font',
    category: 'display'
  },
  {
    id: 'archivo-black',
    name: 'Archivo Black',
    family: 'ArchivoBlack-Regular',
    weight: '400',
    style: 'normal',
    description: 'Heavy, impactful sans-serif',
    category: 'display'
  },
  {
    id: 'barlow',
    name: 'Barlow ExtraBold',
    family: 'Barlow-ExtraBold',
    weight: '800',
    style: 'normal',
    description: 'Strong, geometric sans-serif',
    category: 'sans-serif'
  },
  {
    id: 'bebas-neue',
    name: 'Bebas Neue',
    family: 'BebasNeue-Regular',
    weight: '400',
    style: 'normal',
    description: 'Tall, condensed display font',
    category: 'display'
  },
  {
    id: 'lexend',
    name: 'Lexend ExtraBold',
    family: 'Lexend-ExtraBold',
    weight: '800',
    style: 'normal',
    description: 'Readable, optimized for comprehension',
    category: 'sans-serif'
  },
  {
    id: 'montserrat',
    name: 'Montserrat ExtraBold',
    family: 'Montserrat-ExtraBold',
    weight: '800',
    style: 'normal',
    description: 'Popular, versatile sans-serif',
    category: 'sans-serif'
  },
  {
    id: 'oswald',
    name: 'Oswald Bold',
    family: 'Oswald-Bold',
    weight: '700',
    style: 'normal',
    description: 'Condensed, strong sans-serif',
    category: 'sans-serif'
  },
  {
    id: 'outfit',
    name: 'Outfit ExtraBold',
    family: 'Outfit-ExtraBold',
    weight: '800',
    style: 'normal',
    description: 'Modern, rounded sans-serif',
    category: 'sans-serif'
  },
  {
    id: 'poppins',
    name: 'Poppins ExtraBold',
    family: 'Poppins-ExtraBold',
    weight: '800',
    style: 'normal',
    description: 'Friendly, geometric sans-serif',
    category: 'sans-serif'
  }
];

// Global font manager instance
const fontManager = new FontManager();

// Initialize fonts on module load
fontManager.initialize().catch(error => {
  console.error('[FontManager] Initialization failed:', error);
});

// Export functions for curated font system
export function getCuratedFonts() {
  return CURATED_FONTS.map(font => ({
    id: font.id,
    name: font.name,
    description: font.description,
    category: font.category
  }));
}

export function getCuratedFontById(fontId) {
  return CURATED_FONTS.find(font => font.id === fontId) || null;
}

export function resolveCuratedFont(fontId) {
  const curatedFont = getCuratedFontById(fontId);
  if (!curatedFont) {
    throw new Error(`Unknown font ID: ${fontId}`);
  }

  // Try to find the font in the registry
  const match = fontManager.findBestMatch(curatedFont.family, curatedFont.weight, curatedFont.style);
  if (!match) {
    throw new Error(`Curated font not available: ${curatedFont.name} (${curatedFont.family})`);
  }

  return match;
}

// Export functions for backward compatibility
export function getRegisteredFonts() {
  return fontManager.getDebugInfo();
}

export async function registerUploadedFont(filePath, opts = {}) {
  try {
    // Extract metadata first
    const metadata = await fontManager.extractFontMetadata(filePath);
    
    // Use provided family hint if available
    if (opts.family && opts.family.trim()) {
      metadata.family = opts.family.trim();
    }
    
    // Use provided weight if available
    if (opts.weight) {
      metadata.weight = String(opts.weight);
    }

    // Register the font
    const fontInfo = await fontManager.registerFont(filePath, metadata);
    
    return {
      family: fontInfo.family,
      weight: fontInfo.weight,
      style: fontInfo.style,
      success: true
    };
  } catch (error) {
    console.error('[registerUploadedFont] Failed:', error.message);
    return {
      family: opts.family || 'Unknown',
      weight: opts.weight || '400',
      style: 'normal',
      success: false,
      error: error.message
    };
  }
}

/* -----------------------------------------------------------
 * Timing & word selection (unchanged)
 * -----------------------------------------------------------
 */

function easeOutCubic(p) { return 1 - Math.pow(1 - p, 3); }

function pickActiveWord(t, segments) {
  for (const seg of segments) {
    if (t >= seg.start && t < seg.end + 0.02) {
      const text = String(seg.text || '').trim();
      if (!text) return null;
      const tokens = text.split(/\s+/);
      const segDur = Math.max(0.001, seg.end - seg.start);
      let durs = Array.isArray(seg.wordDurations) && seg.wordDurations.length === tokens.length
        ? seg.wordDurations.slice()
        : tokens.map(() => segDur / Math.max(1, tokens.length));
      const sum = durs.reduce((a, b) => a + b, 0) || 1;
      const scale = segDur / sum;
      durs = durs.map(d => d * scale);
      let tt = seg.start;
      for (let i = 0; i < tokens.length; i++) {
        const start = tt;
        const end = i === tokens.length - 1
          ? Math.min(seg.end + 0.04, tt + durs[i])
          : Math.min(seg.end, tt + durs[i]);
        if (t >= start && t < end + 0.02) {
          const emphasized = Boolean(seg.emphasizeSegment) || Boolean((seg.emphasizedWordIndices || []).includes(i));
          return { text: tokens[i], start, end, emphasized };
        }
        tt = end;
      }
      return null;
    }
  }
  return null;
}

/* -----------------------------------------------------------
 * Public renderer with improved font handling
 * -----------------------------------------------------------
 */

export async function renderOverlayFrames({ framesDir, width, height, fps, segments, style, watermark, videoDuration }) {
  // Ensure font manager is initialized
  if (!fontManager.initialized) {
    await fontManager.initialize();
  }

  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');

  // Use provided video duration, or fallback to segments duration with extra padding
  const segmentDuration = Math.max(0, segments.reduce((m, s) => Math.max(m, s.end || 0), 0));
  const duration = videoDuration || segmentDuration + 1.0; // +1s padding if no videoDuration
  const totalFrames = Math.max(1, Math.round(duration * fps));

  const baseStyle = Object.assign({
    fontId: 'inter', // Use font ID instead of fontFamily
    fontWeight: 800,
    fontSize: 36,
    yOffsetPercent: 40,
    color: '#ffffff',
    shadowColor: '#000000',
    shadowSize: 6,
    uppercase: true,
    emphasizeColor: '#f59e0b',
    emphasizeScale: 1.35,
    wordEffect: 'pop',
    effectOnlyEmphasized: false,
    effectDurationMs: 250
  }, style || {});

  // Scale font size based on resolution (base 720p)
  const baseResolution = 720;
  const scaleFactor = Math.max(width, height) / baseResolution;
  const scaledFontSize = Math.round(Number(baseStyle.fontSize || 36) * scaleFactor);
  baseStyle.fontSize = scaledFontSize;

  // Validate curated font early and fail fast if not available
  let resolvedFont;
  try {
    // Support both old fontFamily and new fontId approaches
    if (baseStyle.fontId) {
      resolvedFont = resolveCuratedFont(baseStyle.fontId);
      console.log(`[Renderer] Using curated font: ${baseStyle.fontId} -> ${resolvedFont.family} ${resolvedFont.weight} ${resolvedFont.style}`);
    } else if (baseStyle.fontFamily) {
      // Fallback for old API
      resolvedFont = fontManager.resolveFontStack(
        baseStyle.fontFamily,
        baseStyle.fontWeight,
        'normal'
      );
      console.log(`[Renderer] Using font stack: ${resolvedFont.family} ${resolvedFont.weight} ${resolvedFont.style}`);
    } else {
      // Default to Inter
      resolvedFont = resolveCuratedFont('inter');
      console.log(`[Renderer] Using default font: ${resolvedFont.family} ${resolvedFont.weight} ${resolvedFont.style}`);
    }
  } catch (error) {
    throw new Error(`Font resolution failed: ${error.message}`);
  }

  for (let frame = 0; frame <= totalFrames; frame++) {
    const t = frame / fps;
    ctx.clearRect(0, 0, width, height);
    ctx.globalCompositeOperation = 'source-over';

    const w = pickActiveWord(t, segments);
    if (w) {
      const apply = !baseStyle.effectOnlyEmphasized || !!w.emphasized;
      const disp = Object.assign({}, baseStyle);
      if (w.emphasized) {
        disp.color = baseStyle.emphasizeColor || baseStyle.color;
        disp.fontSize = Math.round(Number(baseStyle.fontSize) * Number(baseStyle.emphasizeScale || 1.35));
      }
      const dur = Math.max(50, Number(baseStyle.effectDurationMs || 250)) / 1000;
      let p = Math.max(0, Math.min(1, (t - w.start) / dur));
      p = easeOutCubic(p);
      let scale = 1, opacity = 1, ty = 0;
      if (apply) {
        switch (String(baseStyle.wordEffect || 'pop')) {
          case 'fade':  opacity = p; break;
          case 'slide': opacity = p; ty = (1 - p) * 14; break;
          case 'scale': scale = 0.6 + 0.4 * p; break;
          case 'pop':
          default:      opacity = 1; scale = 0.85 + 0.15 * p; break;
        }
      }
      drawWord(ctx, baseStyle.uppercase ? String(w.text).toUpperCase() : String(w.text), width, height, disp, { scale, opacity, translateY: ty }, resolvedFont);
    }

    if (watermark) {
      drawWatermark(ctx, width, height);
    }

    const pth = path.join(framesDir, String(frame).padStart(6, '0') + '.png');
    // Low compression PNG for speed
    const buf = canvas.toBuffer('image/png', { compressionLevel: 0 });
    await fsp.writeFile(pth, buf);
  }
}

/* -----------------------------------------------------------
 * Drawing helpers with improved font resolution
 * -----------------------------------------------------------
 */

function drawWord(ctx, text, width, height, style, opts, resolvedFont = null) {
  ctx.save();
  const fontPx = Math.max(8, Number(style.fontSize || 36));

  try {
    // Use pre-resolved font if provided, otherwise resolve
    let fontToUse = resolvedFont;
    if (!fontToUse) {
      if (style.fontId) {
        fontToUse = resolveCuratedFont(style.fontId);
      } else if (style.fontFamily) {
        fontToUse = fontManager.resolveFontStack(
          style.fontFamily,
          style.fontWeight || 400,
          'normal'
        );
      } else {
        fontToUse = resolveCuratedFont('inter');
      }
    }

    // Build font specification for canvas
    const cssWeight = Number(fontToUse.weight) >= 600 ? 'bold' : 'normal';
    const cssStyle = fontToUse.style === 'italic' ? 'italic' : 'normal';
    const familyName = /\s/.test(fontToUse.family) ? `'${fontToUse.family}'` : fontToUse.family;
    const fontSpec = `${cssStyle} ${cssWeight} ${fontPx}px ${familyName}`;

    ctx.font = fontSpec;

  } catch (error) {
    console.error('[drawWord] Font resolution failed:', error.message);
    // Fallback to basic font
    ctx.font = `bold ${fontPx}px sans-serif`;
  }

  ctx.textAlign = 'center';
  ctx.textBaseline = 'alphabetic';
  const y = height - Math.round(height * (Number(style.yOffsetPercent || 40) / 100));
  ctx.translate(Math.round(width / 2), Math.round(y + (Number(opts?.translateY) || 0)));
  const scl = Number.isFinite(opts?.scale) ? Number(opts.scale) : 1;
  if (scl !== 1) ctx.scale(scl, scl);

  const shadowSize = Math.max(0, Number(style.shadowSize || 0));
  const shadowColor = style.shadowColor || '#000000';
  const textColor = style.color || '#ffffff';
  const opacity = Number.isFinite(opts?.opacity) ? Math.max(0, Math.min(1, Number(opts.opacity))) : 1;

  // Built-in shadow for smooth rendering
  if (shadowSize > 0) {
    ctx.shadowColor = shadowColor;
    ctx.shadowBlur = shadowSize;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = Math.max(1, Math.round(shadowSize * 0.2));
  } else {
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
  }

  ctx.save();
  ctx.globalAlpha = opacity;
  ctx.fillStyle = textColor;
  ctx.fillText(String(text), 0, 0);
  ctx.restore();

  ctx.restore();
}

function drawWatermark(ctx, width, height) {
  ctx.save();
  const wmText = 'TRIAL EXPORT — Upgrade for no watermark ($9/mo)';

  try {
    // Use font manager for watermark font too
    const resolvedFont = fontManager.resolveFontStack('DejaVu Sans, sans-serif', 700, 'normal');
    const cssWeight = Number(resolvedFont.weight) >= 600 ? 'bold' : 'normal';
    const familyName = /\s/.test(resolvedFont.family) ? `'${resolvedFont.family}'` : resolvedFont.family;
    
    const size = Math.round(Math.max(18, width * 0.03));
    ctx.globalAlpha = 0.85;
    ctx.font = `${cssWeight} ${size}px ${familyName}`;
  } catch (error) {
    // Fallback
    const size = Math.round(Math.max(18, width * 0.03));
    ctx.globalAlpha = 0.85;
    ctx.font = `bold ${size}px sans-serif`;
  }

  ctx.textAlign = 'center';
  ctx.textBaseline = 'bottom';

  const metrics = ctx.measureText(wmText);
  const padX = 18, padY = 10;
  const textW = metrics.width;
  const boxW = textW + padX * 2;
  const boxH = Math.round(ctx.font.match(/\d+/)[0]) + padY * 2;
  const x = Math.round(width / 2);
  const y = height - Math.round(height * 0.06);

  ctx.fillStyle = 'rgba(0,0,0,0.35)';
  ctx.fillRect(x - boxW / 2, y - boxH + 6, boxW, boxH);

  ctx.fillStyle = '#ffffff';
  ctx.fillText(wmText, x, y);
  ctx.restore();
}

// Export font manager for external use
export { fontManager };
