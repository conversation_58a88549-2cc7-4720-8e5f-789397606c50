function getCookie(name: string, cookie: string | null) {
  const m = cookie?.match(new RegExp(`(?:^|; )${name}=([^;]+)`));
  return m ? decodeURIComponent(m[1]) : null;
}

export async function onRequestPost(ctx: any) {
  const { request, env } = ctx;

  // Require authenticated session
  const sid = getCookie("sid", request.headers.get("cookie"));
  if (!sid) return new Response(JSON.stringify({ error: "unauthorized" }), { status: 401 });
  const raw = await env.SESSIONS.get(`sid:${sid}`);
  if (!raw) return new Response(JSON.stringify({ error: "unauthorized" }), { status: 401 });
  const sess = JSON.parse(raw);
  const userId = String(sess.userId || sess.id || "unknown");

  const ctype = request.headers.get("content-type") || "";
  if (!ctype.includes("multipart/form-data")) {
    return new Response(
      JSON.stringify({ error: "multipart/form-data required with file field" }),
      { status: 415 }
    );
  }

  const form = await request.formData();
  const file = form.get("file") as File | null;
  const familyHint = (form.get("family") as string | null) || null;
  if (!file) return new Response(JSON.stringify({ error: "file missing" }), { status: 400 });

  const bucket = (env as any).bucket || (env as any).R2 || (env as any).BUCKET;
  if (!bucket) {
    return new Response(JSON.stringify({ error: "r2_not_configured" }), { status: 500 });
  }

  // Configurable limits with sensible defaults for fonts
  const maxFontMb = Number(env.MAX_FONT_MB || 15);

  // Validate file type and size early (node-canvas supports TTF/OTF)
  const filename = (file.name || "font.ttf").toLowerCase();
  const isTtf = filename.endsWith(".ttf");
  const isOtf = filename.endsWith(".otf");
  if (!isTtf && !isOtf) {
    return new Response(JSON.stringify({ error: "unsupported_type", allowed: [".ttf", ".otf"] }), { status: 415 });
  }
  if (Number.isFinite(maxFontMb) && file.size > maxFontMb * 1024 * 1024) {
    return new Response(JSON.stringify({ error: "file_too_large", maxMb: maxFontMb }), { status: 413 });
  }

  // Store under temporary, per-user font prefix
  const origName = (file.name || "font.ttf").replace(/[^\w.\-]+/g, "_").slice(0, 120);
  const key = `uploads/fonts/${userId}/${crypto.randomUUID()}-${origName}`;

  const put = await bucket.put(key, file.stream(), {
    httpMetadata: {
      contentType: isTtf ? "font/ttf" : "font/otf",
    },
  });

  // Short-lived token to download this font for preview/export
  const token = crypto.randomUUID();
  await env.SESSIONS.put(`dl:${token}`, key, { expirationTtl: 60 * 120 });
  const origin = new URL(request.url);
  const fileURL = `${origin.origin}/api/file?key=${encodeURIComponent(key)}&token=${encodeURIComponent(token)}`;

  return new Response(JSON.stringify({ key, fileURL, family: familyHint || null }), { headers: { "content-type": "application/json" } });
}

